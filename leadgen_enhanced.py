"""leadgen_enhanced.py
-----------------------------------------------------------------------
Enhanced Lead Generation with Personalized Email Generation (v1.0.0)
-----------------------------------------------------------------------
New Features:
* Excel input processing with company data from column D "Заказчик (Из Контур.Закупки)"
* Personalized email generation using Nova AI templates
* PDF generation for each company
* AM folder organization system
* Integration with existing company profile collection
"""
from __future__ import annotations

import asyncio
import glob
import os
import re
import sys
import unittest
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
import argparse

try:
    from dotenv import load_dotenv  # type: ignore
    load_dotenv()
except ModuleNotFoundError:
    pass

try:
    import openai  # type: ignore
    from openai.types.chat import ChatCompletion, ChatCompletionMessageParam  # type: ignore
except ModuleNotFoundError:
    openai = None  # type: ignore
    ChatCompletion = Any  # type: ignore
    ChatCompletionMessageParam = Dict[str, Any]  # type: ignore

try:
    import pandas as pd  # type: ignore
except ModuleNotFoundError:
    pd = None  # type: ignore

try:
    from docx import Document  # type: ignore
    from docx.shared import Inches  # type: ignore
    from docx.enum.text import WD_ALIGN_PARAGRAPH  # type: ignore
    DOCX_AVAILABLE = True
except ModuleNotFoundError:
    print("Warning: python-docx not installed. Word document generation will not work.")
    Document = None
    DOCX_AVAILABLE = False

# Keep reportlab as fallback option
try:
    from reportlab.lib.pagesizes import A4  # type: ignore
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer  # type: ignore
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle  # type: ignore
    from reportlab.lib.units import inch  # type: ignore
    from reportlab.pdfbase import pdfmetrics  # type: ignore
    from reportlab.pdfbase.ttfonts import TTFont  # type: ignore
    REPORTLAB_AVAILABLE = True
except ModuleNotFoundError:
    print("Warning: reportlab not installed. PDF generation will not work.")
    SimpleDocTemplate = None
    REPORTLAB_AVAILABLE = False

OPENAI_AVAILABLE = openai is not None

# --------------------------------------------------------------------
# Config
# --------------------------------------------------------------------
# Model configuration
RESEARCH_MODEL = "gpt-4o-search-preview-2025-03-11"  # For company research with search capabilities
EMAIL_MODEL = "o4-mini-2025-04-16"  # For personalized email generation (o4-mini model)
TIMEOUT_SECS = 300

# o4-mini model specific settings (reasoning_effort may not be supported)
O4_REASONING_EFFORT = "medium"  # Options: "low", "medium", "high" (fallback if not supported)

SECTIONS: Dict[int, str] = {
    1: "Организационная структура",
    2: "Департамент ML / Data Science",
    3: "Основные ML/AI‑проекты",
    4: "Технологический стек и DevOps",
    5: "Внешние цифровые сервисы и продукты",
    6: "Контакты LPR + телефоны/e‑mail",
    7: "Релевантные закупки с kontur.zakupki",
}

# System prompt for company research
SYSTEM_PROMPT = (
    "You are an expert web-researcher with full browsing toolkit. "
    "Given a narrow instruction, collect **bullet facts**. For each fact, "
    "return one line: '<суть факта> — <URL>, <дата>'. "
    "Ответ на русском. Не фантазируй, опирайся только на найденную информацию.\n\n"
    "Получив запрос, разбей его на микротемы и сформируй 5-10 точечных поисковых запросов "
    "(Google + внутренние поиски площадок).\n\n"
    "Используй расширенные операторы (site:, filetype:pdf, кавычки, Boolean-AND/OR/-`) "
    "и фильтры самих сайтов (HH, YouTube, Rusprofile, zakupki.gov.ru и др.).\n\n"
    "Собери не меньше 8 уникальных фактов; каждый факт — с разного URL, обязательно указана "
    "календарная дата ≤ 30 дней точности.\n\n"
    "Вывод строго списком, один факт — одна строка в формате:\n"
    "    <суть факта> — <URL>, <дата в формате ДД.ММ.ГГГГ>\n\n"
    "Язык ответа — русский. Без дополнительных комментариев, без дубликатов ссылок.\n\n"
    "Если релевантных данных нет — выведи `Не найдено`."
)

SECTION_QUERIES: Dict[int, List[str]] = {
    1: [
        "Собери сведения об оргструктуре только {company}: органы управления, IT‑дирекции и т.д. Используй официальный сайт, PDF‑отчёты, Rusprofile, TAdviser, CNews, CIPR, вакансии и YouTube.",
        "Найди интервью топ‑менеджеров {company} (CEO, CIO) на YouTube/Podcasts/VC.ru. Извлеки цитаты и даты.",
    ],
    2: [
        "Вакансии HH/LinkedIn/Superjob только для {company} ('Data Scientist', 'ML Engineer', 'CIO', 'CTO', 'Head of ML'). Выяви наличие DS‑департамента, размер и подчинение.",
        "Статьи Habr/Medium, репозитории GitHub сотрудников {company} с упоминанием ML‑команд. Факты о командах/руководителях.",
    ],
    3: [
        "TAdviser, CNews, ComNews, пресс‑релизы только {company}: ML/AI‑проекты, цели, модели, метрики.",
        "Конференции AI Journey/MLConf/Highload: доклады {company} о проектax ML. Описание и ссылка.",
    ],
    4: [
        "Вакансии DevOps/SRE только {company}: K8s, Docker, Helm, GitLab, Terraform. Ссылки на вакансии.",
        "Тендеры только zakupki.gov.ru/RTS/SberTender по {company}+Kubernetes/CI-CD. Факты об инфраструктуре.",
    ],
    5: [
        "App Store/Google Play/RuStore: приложения только{company}. Название, дата, ссылка.",
        "Официальный сайт/пресс‑релизы только {company}: личные кабинеты, чат‑боты, онлайн‑сервисы. Ссылки.",
    ],
    6: [
        "Rusprofile/SPARK: e‑mail/телефон CTO, CIO, Head of ML/DS только {company}. Ссылки, даты.",
        "LinkedIn/VK/Telegram: публичные визитки топов только {company} с контактами (site:linkedin.com/in, site:t.me).",
        "Сайт только {company} → раздел 'Поставщикам/Контакты': общий телефон/почта для поставщиков.",
    ],
}

# Email generation prompt
EMAIL_GENERATION_PROMPT = """You are a Russian-speaking B2B copywriter who turns raw briefs into personalised outbound emails.
Ваша задача: на основе приложенных файлов сформировать готовое ценностное предложение (e-mail) для конкретной компании-получателя.
Требования к результату:
1. Письмо должно быть написано на русском, вежливым деловым тоном, обращение на «Вы». Письмо должно быть лаконичным и связным.
2. Используйте шаблон письма как каркас; меняйте/дополняйте только те блоки, где указана кастомизация.
3. Персонализация: Проанализируйте профиль компании из файла "Профиль компании [название компании]" на основе чего примените релевантную информацию о компании к письму.  
4. Обязательно укажите выгоды Nova AI, которые напрямую закрывают проблемы, выявленные в этом профиле. При выборе выгод опирайтесь на файл «Позиционирование Nova AI».
5. Не переписывайте весь шаблон: неизменяемые части (приветствие, перечисление общих преимуществ, завершающий абзац-призыв) оставьте как есть.
6. Структура итогового ответа: только готовое письмо, без пояснений и метаданных.
Если в профиле не нашлось данных для определённого пункта плана, пропустите соответствующую кастомизацию, не оставляя пустых плейсхолдеров.
Файлы вложены:
1. «Профиль компании.pdf» — детальный контекст о компании-получателе (организация, проекты, стек, закупки).
2. «Позиционирование Nova AI.pdf» — описание нашего продукта и его преимуществ.
3. «Шаблон письма Nova AI.pdf» — базовый текст письма без персонализации.
Инструкция:  
• Используй план кастомизации, чтобы связать данные из профиля (источник) с шаблонными фразами.  
• Сгенерируй персонализированное письмо для этой компании, вписав конкретные факты вместо плейсхолдеров «[ … ]».  
• В итоговом письме не должно остаться квадратных скобок или плейсхолдеров.
Выведи, пожалуйста, только финальный, полностью готовый e-mail."""

def _build_user_prompt(company: str, inn: str, instruction: str) -> str:
    """Build user prompt with company name and INN for GPT search"""
    company_info = company
    if inn:
        company_info = f"{company} (ИНН: {inn})"
    return instruction.format(company=company_info)

# --------------------------------------------------------------------
# GPT helpers (from original)
# --------------------------------------------------------------------
async def _run_subquery(client: "openai.AsyncClient", company: str, inn: str, instr: str, model: str = RESEARCH_MODEL) -> str:
    # В системный промпт передаем только название компании (для совместимости с существующими промптами)
    sys_msg = SYSTEM_PROMPT.replace("{company}", company)

    # В пользовательский промпт передаем название + ИНН для более точного поиска
    msgs: List[ChatCompletionMessageParam] = [
        {"role": "system", "content": sys_msg},
        {"role": "user", "content": _build_user_prompt(company, inn, instr)},
    ]
    resp: ChatCompletion = await client.chat.completions.create(
        model=model,
        messages=msgs,
        timeout=TIMEOUT_SECS,
    )
    return resp.choices[0].message.content or ""

def _filter_by_company(lines: List[str], company: str) -> List[str]:
    c_lower = company.lower()
    return [ln for ln in lines if c_lower in ln.lower()]

async def _gather_section(client: "openai.AsyncClient", company: str, inn: str, idx: int, model: str = RESEARCH_MODEL) -> str:
    raw: List[str] = []
    for q in SECTION_QUERIES[idx]:
        try:
            chunk = await _run_subquery(client, company, inn, q, model)
        except Exception as exc:
            chunk = f"Ошибка: {exc}"
        raw.extend(chunk.strip().split("\n"))

    raw = [ln for ln in raw if ln]
    uniq, seen = [], set()
    for ln in raw:
        if ln not in seen:
            uniq.append(ln)
            seen.add(ln)

    rel = _filter_by_company(uniq, company)
    if rel:
        return "\n".join(rel)

    urls = {m.group(0) for ln in uniq for m in re.finditer(r"https?://[^ ,]+", ln)}
    return f"Информация отсутствует. Проверены источники: {', '.join(sorted(urls)) or '—'}"

def _scan_tenders(dir_path: Optional[str], company: str) -> str:
    if dir_path is None:
        return "Не задан каталог с таблицами закупок (--tenders)"
    if pd is None:
        return "pandas не установлен, сканирование закупок невозможно"

    bullets: List[str] = []
    comp_low = company.lower()
    for file in glob.glob(os.path.join(dir_path, "*.xls*")):
        try:
            df = pd.read_excel(file, header=None, engine="openpyxl")  # type: ignore[arg-type]
        except Exception:
            continue
        ab_col = 27  # AB
        for i in range(2, len(df)):
            cell = str(df.iat[i, ab_col]).lower() if not pd.isna(df.iat[i, ab_col]) else ""
            if comp_low in cell:
                name = str(df.iat[i, 2]).strip() if not pd.isna(df.iat[i, 2]) else ""
                price = str(df.iat[i, 3]).strip() if not pd.isna(df.iat[i, 3]) else ""
                date = str(df.iat[i, 11]).strip() if not pd.isna(df.iat[i, 11]) else ""
                bullets.append(f"Название: {name} — НМЦ: {price} — Дата публикации: {date}")
    return "Не найдено" if not bullets else "\n".join(sorted(set(bullets)))

# --------------------------------------------------------------------
# Company profile collection (from original)
# --------------------------------------------------------------------
async def _collect(company: str, inn: str, tenders_dir: Optional[str], research_model: str = RESEARCH_MODEL) -> Dict[int, str]:
    if not OPENAI_AVAILABLE:
        raise ImportError("openai package required for sections 1‑6")

    client = openai.AsyncClient(api_key=os.getenv("OPENAI_API_KEY"))  # type: ignore[arg-type]
    res: Dict[int, str] = {}
    tasks = {i: asyncio.create_task(_gather_section(client, company, inn, i, research_model)) for i in range(1, 7)}
    for i, t in tasks.items():
        res[i] = await t
    res[7] = _scan_tenders(tenders_dir, company)
    return res

async def collect_company_profile_async(company: str, inn: str = "", tenders_dir: Optional[str] = None, research_model: str = RESEARCH_MODEL) -> str:
    """Collect company profile and return as markdown string (async version)"""
    if not OPENAI_AVAILABLE:
        raise ImportError("Install the 'openai' package: pip install openai")
    if not os.getenv("OPENAI_API_KEY"):
        raise EnvironmentError("OPENAI_API_KEY not set (.env or export)")

    sections = await _collect(company, inn, tenders_dir, research_model)

    company_title = f"{company}"
    if inn:
        company_title = f"{company} (ИНН: {inn})"

    md: List[str] = [f"**Профиль компании «{company_title}»**\n"]
    for idx in sorted(SECTIONS):
        md.append(f"### {idx}. {SECTIONS[idx]}\n")
        md.append((sections[idx] or "Не найдено") + "\n")
    return "\n".join(md)

def collect_company_profile(company: str, inn: str = "", tenders_dir: Optional[str] = None, research_model: str = RESEARCH_MODEL) -> str:
    """Collect company profile and return as markdown string (sync wrapper)"""
    if not OPENAI_AVAILABLE:
        raise ImportError("Install the 'openai' package: pip install openai")
    if not os.getenv("OPENAI_API_KEY"):
        raise EnvironmentError("OPENAI_API_KEY not set (.env or export)")

    try:
        # Try to get existing event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If loop is running, we need to use a different approach
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, collect_company_profile_async(company, inn, tenders_dir, research_model))
                return future.result()
        else:
            return loop.run_until_complete(collect_company_profile_async(company, inn, tenders_dir, research_model))
    except RuntimeError:
        # No event loop exists, create a new one
        return asyncio.run(collect_company_profile_async(company, inn, tenders_dir, research_model))

# --------------------------------------------------------------------
# New functionality: Excel processing and email generation
# --------------------------------------------------------------------

def read_companies_from_excel(
    excel_path: str,
    company_column: str = "D",
    inn_column: str = "E",
    am_column: str = "AM"
) -> List[Tuple[str, str, str]]:
    """Read companies, their INNs and AMs from Excel file with deduplication

    Args:
        excel_path: Path to Excel file
        company_column: Name or letter of the column containing company names (default: "D")
        inn_column: Name or letter of the column containing INNs (default: "E")
        am_column: Name of the column containing AM names (default: "AM")

    Returns:
        List of tuples (company_name, inn, am_name) - deduplicated by company name and INN
    """
    if pd is None:
        raise ImportError("pandas не установлен")

    def get_column_index(df, column_identifier):
        """Get column index by name or letter"""
        if isinstance(column_identifier, str) and len(column_identifier) == 1 and column_identifier.isalpha():
            # Convert letter to index (A=0, B=1, C=2, D=3, etc.)
            return ord(column_identifier.upper()) - ord('A')
        elif column_identifier in df.columns:
            return df.columns.get_loc(column_identifier)
        else:
            return None

    try:
        # Read Excel with all columns as strings to preserve INN leading zeros
        df = pd.read_excel(excel_path, engine="openpyxl", dtype=str, keep_default_na=False)
        companies = []

        # Print column names for debugging
        print(f"Доступные столбцы: {list(df.columns)}")

        # Get column indices
        company_col_index = get_column_index(df, company_column)
        inn_col_index = get_column_index(df, inn_column)

        if company_col_index is None:
            raise ValueError(f"Столбец с названиями компаний '{company_column}' не найден")

        if inn_col_index is None:
            print(f"Предупреждение: Столбец с ИНН '{inn_column}' не найден, ИНН не будут использоваться")

        # Find AM column
        am_col_index = None
        if am_column in df.columns:
            am_col_index = df.columns.get_loc(am_column)
        else:
            # Try to find AM column by common names
            am_variants = ["AM", "АМ", "Account Manager", "Менеджер", "Manager", "Ответственный"]
            for variant in am_variants:
                if variant in df.columns:
                    am_col_index = df.columns.get_loc(variant)
                    print(f"Найден столбец АМ: {variant}")
                    break

        if am_col_index is None:
            print("Предупреждение: Столбец АМ не найден, будет использовано значение по умолчанию")

        # Track processed companies to avoid duplicates
        processed_companies = set()  # Set of (company_name_lower, inn) tuples
        companies = []
        duplicates_count = 0

        # Process rows
        for index, row in df.iterrows():
            # Skip header row if it contains column names
            if index == 0 and company_col_index < len(row):
                cell_value = str(row.iloc[company_col_index]).lower()
                if any(keyword in cell_value for keyword in ["заказчик", "компания", "организация", "название"]):
                    continue

            # Get company name from specified column
            if company_col_index < len(row):
                company_value = row.iloc[company_col_index]
                if company_value is not None and str(company_value).lower() not in ["nan", "none", ""]:
                    company_name = str(company_value).strip()
                else:
                    company_name = ""
            else:
                company_name = ""

            # Get INN from specified column
            inn = ""
            if inn_col_index is not None and inn_col_index < len(row):
                inn_value = row.iloc[inn_col_index]
                if inn_value is not None and str(inn_value).lower() not in ["nan", "none", ""]:
                    inn = str(inn_value).strip()
                    # Handle cases where pandas converts INN to float (e.g., 1234567890.0)
                    if inn.endswith('.0'):
                        inn = inn[:-2]

            # Skip empty or invalid company names
            if not company_name or company_name.lower().strip() in ["nan", "", "заказчик (из контур.закупки)"]:
                continue

            # Create deduplication key
            # Use both company name and INN for deduplication
            company_key = (company_name.lower().strip(), inn.strip())

            # Check if this company was already processed
            if company_key in processed_companies:
                duplicates_count += 1
                print(f"  Пропущен дубликат: {company_name}" + (f" (ИНН: {inn})" if inn else ""))
                continue

            # Add to processed set
            processed_companies.add(company_key)

            # Get AM name
            if am_col_index is not None and am_col_index < len(row):
                am_value = row.iloc[am_col_index]
                if am_value is not None and str(am_value).lower() not in ["nan", "none", ""]:
                    am_name = str(am_value).strip()
                else:
                    am_name = "AM_Default"
            else:
                am_name = "AM_Default"

            companies.append((company_name, inn, am_name))

        print(f"Обработано {len(companies)} уникальных компаний")
        if duplicates_count > 0:
            print(f"Пропущено {duplicates_count} дубликатов")
        return companies

    except Exception as e:
        raise Exception(f"Ошибка при чтении Excel файла: {e}")

def read_pdf_content(pdf_path: str) -> str:
    """Read content from PDF file"""
    try:
        import PyPDF2
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            for page in reader.pages:
                text += page.extract_text()
        return text
    except ImportError:
        raise ImportError("PyPDF2 не установлен. Установите: pip install PyPDF2")
    except Exception as e:
        raise Exception(f"Ошибка при чтении PDF файла {pdf_path}: {e}")

async def generate_personalized_email(
    company_name: str,
    company_profile: str,
    template_pdf_path: str,
    nova_ai_pdf_path: str,
    email_model: str = EMAIL_MODEL,
    reasoning_effort: str = O4_REASONING_EFFORT
) -> str:
    """Generate personalized email using GPT-4"""
    if not OPENAI_AVAILABLE:
        raise ImportError("openai package required")
    
    # Read template and Nova AI positioning
    template_content = read_pdf_content(template_pdf_path)
    nova_ai_content = read_pdf_content(nova_ai_pdf_path)
    
    # Prepare the prompt
    user_content = f"""
Компания: {company_name}

Профиль компании:
{company_profile}

Шаблон письма:
{template_content}

Позиционирование Nova AI:
{nova_ai_content}
"""
    
    client = openai.AsyncClient(api_key=os.getenv("OPENAI_API_KEY"))  # type: ignore[arg-type]
    
    msgs: List[ChatCompletionMessageParam] = [
        {"role": "system", "content": EMAIL_GENERATION_PROMPT},
        {"role": "user", "content": user_content},
    ]
    
    # Use o4-mini model for email generation with reasoning effort
    try:
        resp: ChatCompletion = await client.chat.completions.create(
            model=email_model,
            messages=msgs,
            timeout=TIMEOUT_SECS,
            reasoning_effort=reasoning_effort  # o3 specific parameter
        )
    except Exception as e:
        # Fallback to standard parameters if reasoning_effort is not supported
        if "reasoning_effort" in str(e) or "unexpected keyword argument" in str(e):
            print(f"  ⚠ Параметр reasoning_effort не поддерживается, используем стандартные параметры")
            resp: ChatCompletion = await client.chat.completions.create(
                model=email_model,
                messages=msgs,
                timeout=TIMEOUT_SECS,
            )
        else:
            raise e
    
    return resp.choices[0].message.content or ""

def create_word_document(email_content: str, company_profile: str, output_path: str, company_name: str) -> None:
    """Create Word document with company profile and email content"""
    if not DOCX_AVAILABLE:
        raise ImportError("python-docx не установлен. Установите: pip install python-docx")
    
    # Create new document
    doc = Document()
    
    # Add title
    title = doc.add_heading(f'Ценностное предложение для {company_name}', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add spacing
    doc.add_paragraph()
    
    # Add company profile section
    profile_heading = doc.add_heading('Профиль компании', level=1)
    
    # Add company profile content
    profile_paragraphs = company_profile.split('\n')
    for para in profile_paragraphs:
        if para.strip():
            # Handle markdown headers
            if para.startswith('###'):
                doc.add_heading(para.replace('###', '').strip(), level=2)
            elif para.startswith('**') and para.endswith('**'):
                p = doc.add_paragraph()
                run = p.add_run(para.replace('**', ''))
                run.bold = True
            else:
                doc.add_paragraph(para.strip())
    
    # Add separator
    doc.add_page_break()
    
    # Add email section
    email_heading = doc.add_heading('Персонализированное письмо', level=1)
    
    # Add email content
    email_paragraphs = email_content.split('\n\n')
    for para in email_paragraphs:
        if para.strip():
            doc.add_paragraph(para.strip())
    
    # Save document
    doc.save(output_path)

def create_pdf_document_fallback(email_content: str, company_profile: str, output_path: str, company_name: str) -> None:
    """Fallback PDF creation function (if Word is not available)"""
    if not REPORTLAB_AVAILABLE:
        raise ImportError("reportlab не установлен. Установите: pip install reportlab")
    
    # Create the PDF document
    doc = SimpleDocTemplate(output_path, pagesize=A4)
    styles = getSampleStyleSheet()
    
    # Create custom style for Russian text
    custom_style = ParagraphStyle(
        'CustomStyle',
        parent=styles['Normal'],
        fontName='Helvetica',
        fontSize=12,
        spaceAfter=12,
        encoding='utf-8'
    )
    
    # Build the document
    story = []
    
    # Add title
    title_style = ParagraphStyle(
        'TitleStyle',
        parent=styles['Title'],
        fontName='Helvetica-Bold',
        fontSize=16,
        spaceAfter=20
    )
    
    story.append(Paragraph(f"Ценностное предложение для {company_name}", title_style))
    story.append(Spacer(1, 0.2*inch))
    
    # Add company profile
    story.append(Paragraph("Профиль компании", title_style))
    story.append(Spacer(1, 0.1*inch))
    
    profile_paragraphs = company_profile.split('\n\n')
    for para in profile_paragraphs:
        if para.strip():
            story.append(Paragraph(para.strip(), custom_style))
            story.append(Spacer(1, 0.05*inch))
    
    story.append(Spacer(1, 0.2*inch))
    
    # Add email content
    story.append(Paragraph("Персонализированное письмо", title_style))
    story.append(Spacer(1, 0.1*inch))
    
    email_paragraphs = email_content.split('\n\n')
    for para in email_paragraphs:
        if para.strip():
            story.append(Paragraph(para.strip(), custom_style))
            story.append(Spacer(1, 0.1*inch))
    
    # Build PDF
    doc.build(story)

def create_am_folders_and_documents(
    companies_data: List[Tuple[str, str, str, str, str]],  # (company, inn, am, profile, email)
    output_dir: str
) -> None:
    """Create AM folders and Word/PDF documents for each company"""

    # Group companies by AM
    am_companies: Dict[str, List[Tuple[str, str, str, str]]] = {}
    for company, inn, am, profile, email in companies_data:
        if am not in am_companies:
            am_companies[am] = []
        am_companies[am].append((company, inn, profile, email))
    
    # Create output directory if it doesn't exist
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # Create folders and documents for each AM
    for am_name, companies in am_companies.items():
        am_folder = Path(output_dir) / am_name
        am_folder.mkdir(exist_ok=True)

        for company_name, inn, profile, email in companies:
            # Create filename (sanitize company name)
            safe_company_name = re.sub(r'[^\w\s-]', '', company_name).strip()
            safe_company_name = re.sub(r'[-\s]+', '-', safe_company_name)
            
            # Try Word format first, fallback to PDF
            if DOCX_AVAILABLE:
                doc_filename = f"{safe_company_name}.docx"
                doc_path = am_folder / doc_filename
                
                try:
                    create_word_document(email, profile, str(doc_path), company_name)
                    print(f"✓ Создан Word документ: {doc_path}")
                except Exception as e:
                    print(f"✗ Ошибка создания Word документа для {company_name}: {e}")
                    # Try PDF fallback
                    if REPORTLAB_AVAILABLE:
                        pdf_filename = f"{safe_company_name}.pdf"
                        pdf_path = am_folder / pdf_filename
                        try:
                            create_pdf_document_fallback(email, profile, str(pdf_path), company_name)
                            print(f"✓ Создан PDF (fallback): {pdf_path}")
                        except Exception as pdf_e:
                            print(f"✗ Ошибка создания PDF для {company_name}: {pdf_e}")
            else:
                # Use PDF if Word is not available
                if REPORTLAB_AVAILABLE:
                    pdf_filename = f"{safe_company_name}.pdf"
                    pdf_path = am_folder / pdf_filename
                    try:
                        create_pdf_document_fallback(email, profile, str(pdf_path), company_name)
                        print(f"✓ Создан PDF: {pdf_path}")
                    except Exception as e:
                        print(f"✗ Ошибка создания PDF для {company_name}: {e}")
                else:
                    print(f"✗ Нет доступных библиотек для создания документов для {company_name}")

# --------------------------------------------------------------------
# Main processing function
# --------------------------------------------------------------------

async def process_companies_batch(
    excel_path: str,
    template_pdf_path: str,
    nova_ai_pdf_path: str,
    output_dir: str,
    tenders_dir: Optional[str] = None,
    company_column: str = "D",
    inn_column: str = "E",
    am_column: str = "AM",
    research_model: str = RESEARCH_MODEL,
    email_model: str = EMAIL_MODEL,
    reasoning_effort: str = O4_REASONING_EFFORT
) -> None:
    """Main function to process all companies from Excel"""
    
    # Validate input files
    print("Проверка входных файлов...")
    for path, name in [(excel_path, "Excel"), (template_pdf_path, "Template"), (nova_ai_pdf_path, "Nova AI")]:
        if not os.path.exists(path):
            raise FileNotFoundError(f"{name} файл не найден: {path}")
    
    # Validate dependencies
    if not OPENAI_AVAILABLE:
        raise ImportError("OpenAI package не установлен. Установите: pip install openai")
    
    if not os.getenv("OPENAI_API_KEY"):
        raise EnvironmentError("OPENAI_API_KEY не установлен в переменных окружения")
    
    if pd is None:
        raise ImportError("pandas не установлен. Установите: pip install pandas openpyxl")
    
    try:
        import PyPDF2
    except ImportError:
        raise ImportError("PyPDF2 не установлен. Установите: pip install PyPDF2")
    
    if not DOCX_AVAILABLE and not REPORTLAB_AVAILABLE:
        raise ImportError("Не установлены библиотеки для создания документов. Установите: pip install python-docx или pip install reportlab")
    
    print("Чтение компаний из Excel файла...")
    try:
        companies = read_companies_from_excel(excel_path, company_column, inn_column, am_column)
    except Exception as e:
        raise Exception(f"Ошибка чтения Excel файла: {e}")

    if not companies:
        raise ValueError("В Excel файле не найдено ни одной компании для обработки")

    print(f"Найдено {len(companies)} компаний")

    companies_data = []
    failed_companies = []

    for i, (company_name, inn, am_name) in enumerate(companies, 1):
        inn_info = f" (ИНН: {inn})" if inn else ""
        print(f"\n[{i}/{len(companies)}] Обработка: {company_name}{inn_info} (АМ: {am_name})")

        try:
            # Collect company profile
            print("  Сбор профиля компании...")
            company_profile = await collect_company_profile_async(company_name, inn, tenders_dir, research_model)
            
            if not company_profile or len(company_profile.strip()) < 50:
                print("  ⚠ Предупреждение: Получен слишком короткий профиль компании")
            
            # Generate personalized email
            print("  Генерация персонализированного письма...")
            email_content = await generate_personalized_email(
                company_name,
                company_profile,
                template_pdf_path,
                nova_ai_pdf_path,
                email_model,
                reasoning_effort
            )
            
            if not email_content or len(email_content.strip()) < 100:
                print("  ⚠ Предупреждение: Получено слишком короткое письмо")
            
            companies_data.append((company_name, inn, am_name, company_profile, email_content))
            print(f"  ✓ Обработка завершена")
            
        except Exception as e:
            error_msg = f"Ошибка обработки {company_name}: {e}"
            print(f"  ✗ {error_msg}")
            failed_companies.append((company_name, str(e)))
            continue
    
    if not companies_data:
        raise Exception("Не удалось обработать ни одной компании")
    
    # Create AM folders and documents
    print(f"\nСоздание папок АМ и документов в {output_dir}...")
    try:
        create_am_folders_and_documents(companies_data, output_dir)
    except Exception as e:
        raise Exception(f"Ошибка создания документов: {e}")
    
    # Summary
    print(f"\n{'='*50}")
    print(f"✓ Обработка завершена!")
    print(f"✓ Успешно обработано: {len(companies_data)} компаний")
    print(f"✓ Создано документов: {len(companies_data)}")
    
    if failed_companies:
        print(f"✗ Ошибки обработки: {len(failed_companies)} компаний")
        for company, error in failed_companies:
            print(f"  - {company}: {error}")
    
    print(f"📁 Результаты сохранены в: {output_dir}")

# --------------------------------------------------------------------
# CLI interface
# --------------------------------------------------------------------

def _cli() -> None:
    parser = argparse.ArgumentParser(
        description="Enhanced Lead-finder with personalized email generation",
        formatter_class=argparse.RawTextHelpFormatter,
        epilog="""Примеры:
  # Базовое использование
  python leadgen_enhanced.py --excel companies.xlsx --template template.pdf --nova-ai nova_ai.pdf --output ./am_folders

  # С указанием столбцов компаний, ИНН, АМ и каталога тендеров
  python leadgen_enhanced.py --excel companies.xlsx --template template.pdf --nova-ai nova_ai.pdf --output ./am_folders --company-column "C" --inn-column "D" --am-column "Менеджер" --tenders ./tenders

  # Запуск тестов
  python leadgen_enhanced.py --test
""",
    )
    parser.add_argument("--excel", help="Excel файл с компаниями")
    parser.add_argument("--template", help="PDF файл с шаблоном письма Nova AI")
    parser.add_argument("--nova-ai", help="PDF файл с позиционированием Nova AI")
    parser.add_argument("--output", help="Директория для создания папок АМ с документами")
    parser.add_argument("--company-column", default="D", help="Столбец с названиями компаний (буква или название, по умолчанию: 'D')")
    parser.add_argument("--inn-column", default="E", help="Столбец с ИНН компаний (буква или название, по умолчанию: 'E')")
    parser.add_argument("--am-column", default="AM", help="Название столбца с именами АМ (по умолчанию: 'AM')")
    parser.add_argument("--tenders", help="Каталог *.xls[x] файлов с kontur.zakupki (опционально)")
    parser.add_argument("--research-model", default=RESEARCH_MODEL, help=f"Модель для исследования компаний (по умолчанию: {RESEARCH_MODEL})")
    parser.add_argument("--email-model", default=EMAIL_MODEL, help=f"Модель для генерации писем (по умолчанию: {EMAIL_MODEL})")
    parser.add_argument("--reasoning-effort", default=O4_REASONING_EFFORT, choices=["low", "medium", "high"], help=f"Уровень reasoning для o4-mini модели (по умолчанию: {O4_REASONING_EFFORT})")
    parser.add_argument("--test", action="store_true", help="Запустить unit-тесты и выйти")
    args = parser.parse_args()

    if args.test:
        # Run tests
        sys.argv = [sys.argv[0]]
        unittest.main()
        return

    if not all([args.excel, args.template, args.nova_ai, args.output]):
        parser.error("Обязательные параметры: --excel, --template, --nova-ai, --output")

    print("🚀 Запуск Enhanced Lead Generation System")
    print(f"📊 Excel файл: {args.excel}")
    print(f"📄 Шаблон письма: {args.template}")
    print(f"🤖 Nova AI позиционирование: {args.nova_ai}")
    print(f"📁 Выходная директория: {args.output}")
    print(f"🏢 Столбец компаний: {args.company_column}")
    print(f"🔢 Столбец ИНН: {args.inn_column}")
    print(f"👤 Столбец АМ: {args.am_column}")
    print(f"🔍 Модель исследования: {args.research_model}")
    print(f"✉️ Модель писем: {args.email_model}")
    print(f"🧠 Reasoning effort: {args.reasoning_effort}")
    if args.tenders:
        print(f"📋 Каталог тендеров: {args.tenders}")
    print("-" * 50)

    # Run the main processing
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(process_companies_batch(
            args.excel,
            args.template,
            args.nova_ai,
            args.output,
            args.tenders,
            args.company_column,
            args.inn_column,
            args.am_column,
            args.research_model,
            args.email_model,
            args.reasoning_effort
        ))
    except Exception as e:
        print(f"\n❌ Критическая ошибка: {e}")
        sys.exit(1)
    finally:
        loop.close()

if __name__ == "__main__":
    _cli()