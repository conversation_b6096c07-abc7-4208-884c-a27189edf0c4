# Enhanced Lead Generation System

Автоматизированная система генерации персонализированных коммерческих предложений для Nova AI на основе анализа компаний.

## Возможности

- 📊 **Гибкая обработка Excel файлов** с настраиваемыми столбцами для компаний, ИНН и АМ
- 🔢 **Использование ИНН в поиске** - более точная идентификация компаний в поисковых запросах
- � **Автоматическая дедупликация** - исключение повторных обработок одних и тех же компаний
- �🔍 **Автоматический сбор профилей компаний** через GPT-4 с поиском в интернете
- 📧 **Генерация персонализированных писем** на основе шаблона и позиционирования Nova AI
- 📁 **Организация по Account Manager'ам** - создание папок с именами АМ
- 📄 **Создание Word документов** с профилем компании и ценностными предложениями (с fallback на PDF)
- 🔄 **Интеграция с данными закупок** из kontur.zakupki

## Установка

1. Установите зависимости:
```bash
pip3 install -r requirements.txt
```

Если команда `pip3` не найдена, попробуйте:
```bash
python3 -m pip install -r requirements.txt
```

2. Настройте переменные окружения:
```bash
export OPENAI_API_KEY="your-openai-api-key"
```

Или создайте файл `.env`:
```
OPENAI_API_KEY=your-openai-api-key
```

## Использование

### Базовое использование
```bash
python3 leadgen_enhanced.py \
  --excel companies.xlsx \
  --template template.pdf \
  --nova-ai nova_ai.pdf \
  --output ./am_folders
```

### С настройкой столбцов и дополнительными параметрами
```bash
python3 leadgen_enhanced.py \
  --excel companies.xlsx \
  --template template.pdf \
  --nova-ai nova_ai.pdf \
  --output ./am_folders \
  --company-column "C" \
  --inn-column "D" \
  --am-column "Менеджер" \
  python3 leadgen_enhanced.py \
  --excel '/Users/<USER>/Downloads/test_test_test_crm.xlsx' \
  --template '/Users/<USER>/Downloads/Шаблон письма Nova AI (теплый).pdf' \
  --nova-ai '/Users/<USER>/Downloads/Позиционирование Nova AI.pdf' \
  --output '/Users/<USER>/Downloads/am_folders' \
  --company-column "AH" \
  --inn-column "I" \
  --am-column "Клиентский менеджер"
  --tenders '/Users/<USER>/Downloads/kontur.zakupki_AI'
```

### Параметры

- `--excel` - Excel файл с компаниями
- `--template` - PDF файл с шаблоном письма Nova AI
- `--nova-ai` - PDF файл с позиционированием Nova AI
- `--output` - Директория для создания папок АМ с документами
- `--company-column` - Столбец с названиями компаний (буква или название, по умолчанию: "D")
- `--inn-column` - Столбец с ИНН компаний (буква или название, по умолчанию: "E")
- `--am-column` - Название столбца с именами АМ (по умолчанию: "AM")
- `--tenders` - Каталог *.xls[x] файлов с kontur.zakupki (опционально)
- `--research-model` - Модель для исследования компаний (по умолчанию: "gpt-4o-search-preview-2025-03-11")
- `--email-model` - Модель для генерации писем (по умолчанию: "o4-mini-2025-04-16")
- `--reasoning-effort` - Уровень reasoning для o4-mini модели: "low", "medium", "high" (по умолчанию: "medium")

## Структура входных файлов

### Excel файл с компаниями
- **Столбец компаний** (по умолчанию D): Названия компаний (обязательно)
- **Столбец ИНН** (по умолчанию E): ИНН компаний для более точного поиска (опционально)
- **Столбец AM** (или указанный в --am-column): Имена Account Manager'ов
- Поддерживаются варианты названий столбца АМ: "AM", "АМ", "Account Manager", "Менеджер", "Manager", "Ответственный"
- Столбцы можно указывать как буквами (A, B, C, D...), так и названиями столбцов

### PDF файлы
- **template.pdf**: Шаблон письма с плейсхолдерами для персонализации
- **nova_ai.pdf**: Описание продукта Nova AI и его преимуществ

## Выходные данные

Система создает структуру папок:
```
output_directory/
├── AM_Name_1/
│   ├── Company_1.docx
│   ├── Company_2.docx
│   └── ...
├── AM_Name_2/
│   ├── Company_3.docx
│   └── ...
└── ...
```

Каждый Word документ содержит:
1. **Профиль компании** - полный контекст и анализ
2. **Персонализированное письмо** - ценностное предложение Nova AI
3. **Поддержка русского языка** - корректное отображение кириллицы

## Используемые модели

Система использует две разные модели OpenAI для оптимальной производительности:

### Модель исследования: `gpt-4o-search-preview-2025-03-11`
- **Назначение**: Сбор информации о компаниях с поиском в интернете
- **Возможности**: Поиск в реальном времени, анализ веб-страниц, извлечение фактов
- **Использование**: Разделы 1-6 профиля компании

### Модель генерации писем: `o4-mini-2025-04-16`
- **Назначение**: Создание персонализированных коммерческих предложений
- **Возможности**: Эффективное рассуждение, анализ контекста, персонализация
- **Параметры**: Поддержка `reasoning_effort` (low/medium/high) с автоматическим fallback
- **Использование**: Генерация финальных писем на основе собранного контекста

### Настройка моделей
```bash
# Использование других моделей
python3 leadgen_enhanced.py \
  --research-model "gpt-4o" \
  --email-model "o4-mini-2025-04-16" \
  --reasoning-effort "high" \
  # ... другие параметры
```

## Процесс работы

1. **Чтение Excel файла** - извлечение списка компаний, их ИНН и АМ из настраиваемых столбцов
2. **Дедупликация** - автоматическое исключение повторных записей одних и тех же компаний (по названию и ИНН)
3. **Сбор профилей компаний** - автоматический поиск информации через gpt-4o-search:
   - **Поиск в интернете**: использует название + ИНН для более точной идентификации
   - **Поиск по закупкам**: использует только название компании для точного совпадения в Excel таблицах
   - Разделы: организационная структура, ML/Data Science команды, AI проекты, технологический стек, цифровые продукты, контакты руководителей, данные закупок
4. **Генерация писем** - создание персонализированных предложений через o4-mini модель
5. **Создание документов** - формирование Word/PDF документов для каждой компании
6. **Организация по АМ** - группировка файлов по папкам

## Дедупликация компаний

Система автоматически исключает дубликаты компаний на основе:
- **Названия компании** (без учета регистра)
- **ИНН** (если указан)

При обнаружении дубликата выводится сообщение:
```
Пропущен дубликат: Название компании (ИНН: 1234567890)
```

В итоговом отчете показывается количество обработанных уникальных компаний и пропущенных дубликатов.

## Особенности поиска

### Обработка названий компаний
Система автоматически извлекает чистые названия компаний из различных форматов:
- **Обычные кавычки**: `ООО "Компания"` → `Компания`
- **Французские кавычки**: `АО «Рога и Копыта»` → `Рога и Копыта`
- **Типографские кавычки**: `ООО "Компания"` → `Компания`
- **Множественные кавычки**: Выбирается самое длинное название
- **Без кавычек**: Используется исходное название

### Поиск в интернете (GPT-4)
- **Формат запроса**: Чистое название компании (без ИНН, без кавычек)
- **Цель**: Оптимальные поисковые запросы без лишних символов
- **Использование**: Разделы 1-6 профиля компании

### Поиск по закупкам (Excel таблицы)
- **Формат запроса**: Чистое название компании (без ИНН, без кавычек)
- **Цель**: Точное совпадение с данными в столбце AB Excel файлов закупок
- **Использование**: Раздел 7 профиля компании

Это обеспечивает:
- Максимальную эффективность поисковых запросов
- Корректное сопоставление с данными закупок
- Единообразную обработку различных форматов названий

## Требования

- Python 3.8+
- OpenAI API ключ
- Интернет соединение для поиска информации о компаниях

## Зависимости

- `openai` - для работы с GPT-4 API
- `pandas` - для обработки Excel файлов
- `openpyxl` - для чтения Excel файлов
- `python-docx` - для создания Word документов (основной формат)
- `reportlab` - для создания PDF документов (fallback)
- `PyPDF2` - для чтения PDF шаблонов
- `python-dotenv` - для работы с переменными окружения

## Тестирование

Запуск unit-тестов:
```bash
python3 leadgen_enhanced.py --test
```

Запуск проверки системы:
```bash
python3 test_sample.py
```

## Примечания

- Система использует GPT-4 с поисковыми возможностями для сбора актуальной информации
- Обработка происходит последовательно для каждой компании
- Все ошибки логируются с подробным описанием
- Поддерживается graceful degradation при отсутствии данных
- Результаты сохраняются даже при частичных ошибках обработки

## Поддержка

При возникновении проблем проверьте:
1. Корректность API ключа OpenAI
2. Формат входных файлов
3. Доступность интернет соединения
4. Установку всех зависимостей